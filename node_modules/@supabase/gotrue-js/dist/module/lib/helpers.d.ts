import { SupportedStorage } from './types';
export declare function expiresAt(expiresIn: number): number;
export declare function uuid(): string;
export declare const isBrowser: () => boolean;
/**
 * Checks whether localStorage is supported on this browser.
 */
export declare const supportsLocalStorage: () => boolean;
export declare function getParameterByName(name: string, url?: string): string | null;
declare type Fetch = typeof fetch;
export declare const resolveFetch: (customFetch?: Fetch) => Fetch;
export declare const looksLikeFetchResponse: (maybeResponse: unknown) => maybeResponse is Response;
export declare const setItemAsync: (storage: SupportedStorage, key: string, data: any) => Promise<void>;
export declare const getItemAsync: (storage: SupportedStorage, key: string) => Promise<unknown>;
export declare const removeItemAsync: (storage: SupportedStorage, key: string) => Promise<void>;
export declare function decodeBase64URL(value: string): string;
/**
 * A deferred represents some asynchronous work that is not yet finished, which
 * may or may not culminate in a value.
 * Taken from: https://github.com/mike-north/types/blob/master/src/async.ts
 */
export declare class Deferred<T = any> {
    static promiseConstructor: PromiseConstructor;
    readonly promise: PromiseLike<T>;
    readonly resolve: (value?: T | PromiseLike<T>) => void;
    readonly reject: (reason?: any) => any;
    constructor();
}
export declare function decodeJWTPayload(token: string): any;
/**
 * Creates a promise that resolves to null after some time.
 */
export declare function sleep(time: number): Promise<null>;
/**
 * Converts the provided async function into a retryable function. Each result
 * or thrown error is sent to the isRetryable function which should return true
 * if the function should run again.
 */
export declare function retryable<T>(fn: (attempt: number) => Promise<T>, isRetryable: (attempt: number, error: any | null, result?: T) => boolean): Promise<T>;
export declare function generatePKCEVerifier(): string;
export declare function generatePKCEChallenge(verifier: string): Promise<string>;
/**
 * Checks if the current caller of the function is in a {@link
 * #stackGuard} of the provided `name`. Works by looking through
 * the stack trace of an `Error` object for a special function
 * name (generated by {@link #stackGuard}).
 *
 * @param name The name of the stack guard to check for. Must be `[a-zA-Z0-9_-]` only.
 */
export declare function isInStackGuard(name: string): boolean;
/**
 * Creates a minification resistant stack guard, i.e. if you
 * call {@link #isInStackGuard} from within the `fn` parameter
 * function, you will always get `true` otherwise it will be
 * `false`.
 *
 * Works by dynamically defining a function name before calling
 * into `fn`, which is then parsed from the stack trace on an
 * `Error` object within {@link #isInStackGuard}.
 *
 * @param name The name of the stack guard. Must be `[a-zA-Z0-9_-]` only.
 * @param fn The async/await function to be run within the stack guard.
 */
export declare function stackGuard<R>(name: string, fn: () => Promise<R>): Promise<R>;
export {};
//# sourceMappingURL=helpers.d.ts.map