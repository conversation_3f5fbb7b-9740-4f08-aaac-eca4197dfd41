{"version": 3, "file": "SupabaseClient.js", "sourceRoot": "", "sources": ["../../src/SupabaseClient.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAA;AAExD,OAAO,EACL,eAAe,GAGhB,MAAM,wBAAwB,CAAA;AAC/B,OAAO,EAGL,cAAc,GAEf,MAAM,uBAAuB,CAAA;AAC9B,OAAO,EAAE,aAAa,IAAI,qBAAqB,EAAE,MAAM,sBAAsB,CAAA;AAC7E,OAAO,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAA;AACjD,OAAO,EAAE,aAAa,EAAE,MAAM,aAAa,CAAA;AAC3C,OAAO,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,MAAM,eAAe,CAAA;AACxE,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAA;AAG7D,MAAM,sBAAsB,GAAG;IAC7B,OAAO,EAAE,eAAe;CACzB,CAAA;AAED,MAAM,kBAAkB,GAAG;IACzB,MAAM,EAAE,QAAQ;CACjB,CAAA;AAED,MAAM,oBAAoB,GAA8B;IACtD,gBAAgB,EAAE,IAAI;IACtB,cAAc,EAAE,IAAI;IACpB,kBAAkB,EAAE,IAAI;IACxB,QAAQ,EAAE,UAAU;CACrB,CAAA;AAED,MAAM,wBAAwB,GAA0B,EAAE,CAAA;AAE1D;;;;GAIG;AACH,MAAM,CAAC,OAAO,OAAO,cAAc;IA4BjC;;;;;;;;;;;OAWG;IACH,YACY,WAAmB,EACnB,WAAmB,EAC7B,OAA2C;;QAFjC,gBAAW,GAAX,WAAW,CAAQ;QACnB,gBAAW,GAAX,WAAW,CAAQ;QAG7B,IAAI,CAAC,WAAW;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAC7D,IAAI,CAAC,WAAW;YAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAA;QAE7D,MAAM,YAAY,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAA;QAEpD,IAAI,CAAC,WAAW,GAAG,GAAG,YAAY,cAAc,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;QACxE,IAAI,CAAC,OAAO,GAAG,GAAG,YAAY,UAAU,CAAA;QACxC,IAAI,CAAC,UAAU,GAAG,GAAG,YAAY,aAAa,CAAA;QAC9C,IAAI,CAAC,YAAY,GAAG,GAAG,YAAY,eAAe,CAAA;QAElD,mEAAmE;QACnE,MAAM,iBAAiB,GAAG,MAAM,IAAI,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,aAAa,CAAA;QACzF,MAAM,QAAQ,GAAG;YACf,EAAE,EAAE,kBAAkB;YACtB,QAAQ,EAAE,wBAAwB;YAClC,IAAI,kCAAO,oBAAoB,KAAE,UAAU,EAAE,iBAAiB,GAAE;YAChE,MAAM,EAAE,sBAAsB;SAC/B,CAAA;QAED,MAAM,QAAQ,GAAG,oBAAoB,CAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,EAAE,QAAQ,CAAC,CAAA;QAE9D,IAAI,CAAC,UAAU,GAAG,MAAA,MAAA,QAAQ,CAAC,IAAI,0CAAE,UAAU,mCAAI,EAAE,CAAA;QACjD,IAAI,CAAC,OAAO,GAAG,MAAA,MAAA,QAAQ,CAAC,MAAM,0CAAE,OAAO,mCAAI,EAAE,CAAA;QAE7C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,uBAAuB,CACtC,MAAA,QAAQ,CAAC,IAAI,mCAAI,EAAE,EACnB,IAAI,CAAC,OAAO,EACZ,MAAA,QAAQ,CAAC,MAAM,0CAAE,KAAK,CACvB,CAAA;QACD,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,WAAW,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,MAAA,QAAQ,CAAC,MAAM,0CAAE,KAAK,CAAC,CAAA;QAEhG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,mBAAmB,iBAAG,OAAO,EAAE,IAAI,CAAC,OAAO,IAAK,QAAQ,CAAC,QAAQ,EAAG,CAAA;QACzF,IAAI,CAAC,IAAI,GAAG,IAAI,eAAe,CAAC,GAAG,YAAY,UAAU,EAAE;YACzD,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,MAAM,EAAE,MAAA,QAAQ,CAAC,EAAE,0CAAE,MAAM;YAC3B,KAAK,EAAE,IAAI,CAAC,KAAK;SAClB,CAAC,CAAA;QAEF,IAAI,CAAC,oBAAoB,EAAE,CAAA;IAC7B,CAAC;IAED;;OAEG;IACH,IAAI,SAAS;QACX,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE;YAC5C,OAAO,EAAE,IAAI,CAAC,OAAO;YACrB,WAAW,EAAE,IAAI,CAAC,KAAK;SACxB,CAAC,CAAA;IACJ,CAAC;IAED;;OAEG;IACH,IAAI,OAAO;QACT,OAAO,IAAI,qBAAqB,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;IAC7E,CAAC;IAUD;;;;OAIG;IACH,IAAI,CAAC,QAAgB;QACnB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;IACjC,CAAC;IAED;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,GAAG,CAID,EAAgB,EAChB,OAA0B,EAAE,EAC5B,OAGC;QAUD,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,IAAI,EAAE,OAAO,CAAC,CAAA;IACzC,CAAC;IAED;;;;;;OAMG;IACH,OAAO,CAAC,IAAY,EAAE,OAA+B,EAAE,MAAM,EAAE,EAAE,EAAE;QACjE,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAC1C,CAAC;IAED;;OAEG;IACH,WAAW;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAA;IACpC,CAAC;IAED;;;;;OAKG;IACH,aAAa,CAAC,OAAwB;QACpC,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAA;IAC7C,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,CAAA;IAC1C,CAAC;IAEa,eAAe;;;YAC3B,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAA;YAE7C,OAAO,MAAA,MAAA,IAAI,CAAC,OAAO,0CAAE,YAAY,mCAAI,IAAI,CAAA;;KAC1C;IAEO,uBAAuB,CAC7B,EACE,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,OAAO,EACP,UAAU,EACV,QAAQ,EACR,KAAK,GACqB,EAC5B,OAAgC,EAChC,KAAa;QAEb,MAAM,WAAW,GAAG;YAClB,aAAa,EAAE,UAAU,IAAI,CAAC,WAAW,EAAE;YAC3C,MAAM,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE;SAC9B,CAAA;QACD,OAAO,IAAI,kBAAkB,CAAC;YAC5B,GAAG,EAAE,IAAI,CAAC,OAAO;YACjB,OAAO,kCAAO,WAAW,GAAK,OAAO,CAAE;YACvC,UAAU,EAAE,UAAU;YACtB,gBAAgB;YAChB,cAAc;YACd,kBAAkB;YAClB,OAAO;YACP,QAAQ;YACR,KAAK;YACL,KAAK;SACN,CAAC,CAAA;IACJ,CAAC;IAEO,mBAAmB,CAAC,OAA8B;QACxD,OAAO,IAAI,cAAc,CAAC,IAAI,CAAC,WAAW,kCACrC,OAAO,KACV,MAAM,gBAAO,EAAE,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,EAAK,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,KAC7D,CAAA;IACJ,CAAC;IAEO,oBAAoB;QAC1B,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACxD,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,QAAQ,EAAE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,YAAY,CAAC,CAAA;QAClE,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACb,CAAC;IAEO,mBAAmB,CACzB,KAAsB,EACtB,MAA4B,EAC5B,KAAc;QAEd,IACE,CAAC,KAAK,KAAK,iBAAiB,IAAI,KAAK,KAAK,WAAW,CAAC;YACtD,IAAI,CAAC,kBAAkB,KAAK,KAAK,EACjC;YACA,oBAAoB;YACpB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,IAAI,CAAC,CAAA;YAEpC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAA;SAChC;aAAM,IAAI,KAAK,KAAK,YAAY,EAAE;YACjC,mBAAmB;YACnB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACvC,IAAI,MAAM,IAAI,SAAS;gBAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;YAC5C,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAA;SACpC;IACH,CAAC;CACF"}