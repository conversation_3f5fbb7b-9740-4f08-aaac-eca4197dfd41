{"version": 3, "file": "SupabaseClient.d.ts", "sourceRoot": "", "sources": ["../../src/SupabaseClient.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAA;AAExD,OAAO,EACL,eAAe,EACf,sBAAsB,EACtB,qBAAqB,EACtB,MAAM,wBAAwB,CAAA;AAC/B,OAAO,EACL,eAAe,EACf,sBAAsB,EACtB,cAAc,EAEf,MAAM,uBAAuB,CAAA;AAC9B,OAAO,EAAE,aAAa,IAAI,qBAAqB,EAAE,MAAM,sBAAsB,CAAA;AAI7E,OAAO,EAAE,kBAAkB,EAAE,MAAM,0BAA0B,CAAA;AAC7D,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,qBAAqB,EAA6B,MAAM,aAAa,CAAA;AAmBpG;;;;GAIG;AACH,MAAM,CAAC,OAAO,OAAO,cAAc,CACjC,QAAQ,GAAG,GAAG,EACd,UAAU,SAAS,MAAM,GAAG,MAAM,QAAQ,GAAG,QAAQ,SAAS,MAAM,QAAQ,GACxE,QAAQ,GACR,MAAM,GAAG,MAAM,QAAQ,EAC3B,MAAM,SAAS,aAAa,GAAG,QAAQ,CAAC,UAAU,CAAC,SAAS,aAAa,GACrE,QAAQ,CAAC,UAAU,CAAC,GACpB,GAAG;IAkCL,SAAS,CAAC,WAAW,EAAE,MAAM;IAC7B,SAAS,CAAC,WAAW,EAAE,MAAM;IAjC/B;;OAEG;IACH,IAAI,EAAE,kBAAkB,CAAA;IACxB,QAAQ,EAAE,cAAc,CAAA;IAExB,SAAS,CAAC,WAAW,EAAE,MAAM,CAAA;IAC7B,SAAS,CAAC,OAAO,EAAE,MAAM,CAAA;IACzB,SAAS,CAAC,UAAU,EAAE,MAAM,CAAA;IAC5B,SAAS,CAAC,YAAY,EAAE,MAAM,CAAA;IAC9B,SAAS,CAAC,IAAI,EAAE,eAAe,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;IACrD,SAAS,CAAC,UAAU,EAAE,MAAM,CAAA;IAC5B,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAA;IACvB,SAAS,CAAC,kBAAkB,CAAC,EAAE,MAAM,CAAA;IAErC,SAAS,CAAC,OAAO,EAAE;QACjB,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KACtB,CAAA;IAED;;;;;;;;;;;OAWG;gBAES,WAAW,EAAE,MAAM,EACnB,WAAW,EAAE,MAAM,EAC7B,OAAO,CAAC,EAAE,qBAAqB,CAAC,UAAU,CAAC;IA2C7C;;OAEG;IACH,IAAI,SAAS,oBAKZ;IAED;;OAEG;IACH,IAAI,OAAO,0BAEV;IAED,IAAI,CACF,SAAS,SAAS,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,EACjD,KAAK,SAAS,MAAM,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,EACzC,QAAQ,EAAE,SAAS,GAAG,qBAAqB,CAAC,MAAM,EAAE,KAAK,CAAC;IAC5D,IAAI,CAAC,QAAQ,SAAS,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,EAAE,IAAI,SAAS,MAAM,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,EAC1F,QAAQ,EAAE,QAAQ,GACjB,qBAAqB,CAAC,MAAM,EAAE,IAAI,CAAC;IACtC,IAAI,CAAC,QAAQ,EAAE,MAAM,GAAG,qBAAqB,CAAC,MAAM,EAAE,GAAG,CAAC;IAU1D;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,GAAG,CACD,YAAY,SAAS,MAAM,GAAG,MAAM,MAAM,CAAC,WAAW,CAAC,EACvD,SAAS,SAAS,MAAM,CAAC,WAAW,CAAC,CAAC,YAAY,CAAC,EAEnD,EAAE,EAAE,YAAY,EAChB,IAAI,GAAE,SAAS,CAAC,MAAM,CAAM,EAC5B,OAAO,CAAC,EAAE;QACR,IAAI,CAAC,EAAE,OAAO,CAAA;QACd,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,WAAW,CAAA;KAC1C,GACA,sBAAsB,CACvB,MAAM,EACN,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,GAC9B,SAAS,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,SAAS,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAC1D,SAAS,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,GAC5B,KAAK,GACP,KAAK,EACT,SAAS,CAAC,SAAS,CAAC,CACrB;IAID;;;;;;OAMG;IACH,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,GAAE,sBAAuC,GAAG,eAAe;IAIrF;;OAEG;IACH,WAAW,IAAI,eAAe,EAAE;IAIhC;;;;;OAKG;IACH,aAAa,CAAC,OAAO,EAAE,eAAe,GAAG,OAAO,CAAC,IAAI,GAAG,WAAW,GAAG,OAAO,CAAC;IAI9E;;OAEG;IACH,iBAAiB,IAAI,OAAO,CAAC,CAAC,IAAI,GAAG,WAAW,GAAG,OAAO,CAAC,EAAE,CAAC;YAIhD,eAAe;IAM7B,OAAO,CAAC,uBAAuB;IA+B/B,OAAO,CAAC,mBAAmB;IAO3B,OAAO,CAAC,oBAAoB;IAO5B,OAAO,CAAC,mBAAmB;CAoB5B"}