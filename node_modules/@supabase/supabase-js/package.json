{"name": "@supabase/supabase-js", "version": "2.30.0", "description": "Isomorphic Javascript client for Supabase", "keywords": ["javascript", "typescript", "supabase"], "homepage": "https://github.com/supabase/supabase-js", "bugs": "https://github.com/supabase/supabase-js/issues", "license": "MIT", "author": "Supabase", "files": ["dist", "src"], "main": "dist/main/index.js", "module": "dist/module/index.js", "types": "dist/module/index.d.ts", "sideEffects": false, "repository": "supabase/supabase-js", "scripts": {"clean": "rimraf dist docs/v2", "format": "prettier --write \"{src,test}/**/*.ts\"", "build": "run-s clean format build:*", "build:main": "tsc -p tsconfig.json", "build:module": "tsc -p tsconfig.module.json", "build:umd": "webpack", "types-generate": "dts-gen -m '@supabase/supabase-js' -s", "test": "jest --runInBand", "test:coverage": "jest --runInBand --coverage", "test:db": "cd infra/db && docker-compose down && docker-compose up -d && sleep 5", "test:watch": "jest --watch --verbose false --silent false", "test:clean": "cd infra/db && docker-compose down", "docs": "typedoc --entryPoints src/index.ts --out docs/v2 --includes src/**/*.ts", "docs:json": "typedoc --entryPoints src/index.ts --includes src/**/*.ts --json docs/v2/spec.json --excludeExternals"}, "dependencies": {"@supabase/functions-js": "^2.1.0", "@supabase/gotrue-js": "2.43.1", "@supabase/postgrest-js": "^1.7.0", "@supabase/realtime-js": "^2.7.3", "@supabase/storage-js": "^2.5.1", "cross-fetch": "^3.1.5"}, "devDependencies": {"@types/jest": "^29.2.5", "husky": "^4.3.0", "jest": "^29.3.1", "npm-run-all": "^4.1.5", "prettier": "^2.5.1", "pretty-quick": "^3.1.3", "rimraf": "^3.0.2", "semantic-release-plugin-update-version-in-files": "^1.1.0", "ts-jest": "^29.0.5", "ts-loader": "^8.0.11", "ts-node": "^10.9.1", "typedoc": "^0.22.16", "typescript": "^4.5.5", "webpack": "^5.69.1", "webpack-cli": "^4.9.2"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "jsdelivr": "dist/umd/supabase.js", "unpkg": "dist/umd/supabase.js"}