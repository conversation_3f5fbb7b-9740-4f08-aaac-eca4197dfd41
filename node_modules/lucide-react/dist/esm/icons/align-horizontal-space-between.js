/**
 * lucide-react v0.285.0 - ISC
 */

import createLucideIcon from '../createLucideIcon.js';

const AlignHorizontalSpaceBetween = createLucideIcon(
  "AlignHorizontalSpaceBetween",
  [
    [
      "rect",
      { width: "6", height: "14", x: "3", y: "5", rx: "2", key: "j77dae" }
    ],
    [
      "rect",
      { width: "6", height: "10", x: "15", y: "7", rx: "2", key: "bq30hj" }
    ],
    ["path", { d: "M3 2v20", key: "1d2pfg" }],
    ["path", { d: "M21 2v20", key: "p059bm" }]
  ]
);

export { AlignHorizontalSpaceBetween as default };
//# sourceMappingURL=align-horizontal-space-between.js.map
