
import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { supabase } from '@/lib/supabaseClient';
import { getHwid } from '@/lib/hwid';
import GlobalLoader from '@/components/GlobalLoader';

const AdminAccessGuard = ({ children }) => {
  const [isAuthorized, setIsAuthorized] = useState(null);
  const location = useLocation();

  useEffect(() => {
    const checkAuthorization = async () => {
      const hwid = getHwid();
      if (!hwid) {
        setIsAuthorized(false);
        return;
      }

      // If user is already authenticated via session, trust it.
      const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';
      if (isAuthenticated) {
        setIsAuthorized(true);
        return;
      }

      // Otherwise, check if their HWID is registered as an admin.
      const { data, error } = await supabase.rpc('is_admin_hwid', { hwid_to_check: hwid });

      if (error || !data) {
        setIsAuthorized(false);
      } else {
        setIsAuthorized(true);
      }
    };

    checkAuthorization();
  }, []);

  if (isAuthorized === null) {
    return <GlobalLoader />;
  }

  if (!isAuthorized) {
    return <Navigate to="/" replace />;
  }

  // If authorized but not logged in, and not on the login page, redirect to login.
  const isAuthenticated = sessionStorage.getItem('6footscripts_admin_auth') === 'true';
  if (!isAuthenticated && location.pathname !== '/admin/login') {
    return <Navigate to="/admin/login" replace />;
  }

  return children;
};

export default AdminAccessGuard;
