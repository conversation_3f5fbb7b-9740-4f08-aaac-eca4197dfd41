// API client for server-side operations to reduce direct Supabase exposure
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

class ApiClient {
  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    if (config.body && typeof config.body === 'object') {
      config.body = JSON.stringify(config.body);
    }

    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    return response.json();
  }

  // User tracking methods
  async trackUser(hwid, path) {
    return this.request('/track-user', {
      method: 'POST',
      body: { hwid, path }
    });
  }

  async checkUserStatus(hwid) {
    return this.request(`/user-status/${hwid}`);
  }

  // Script methods (if you want to move these too)
  async getScripts() {
    return this.request('/scripts');
  }

  async rateScript(scriptId, rating, hwid) {
    return this.request('/rate-script', {
      method: 'POST',
      body: { scriptId, rating, hwid }
    });
  }
}

export const apiClient = new ApiClient();
