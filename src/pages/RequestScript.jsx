
import React, { useState, useEffect, useCallback } from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { FilePlus, Send, Info, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import PageWrapper from '@/components/PageWrapper';
import { supabase } from '@/lib/supabaseClient';

const RequestScript = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    gameName: '',
    gameLink: '',
    scriptDescription: '',
    discordUsername: '',
  });
  const [agreed, setAgreed] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [mathProblem, setMathProblem] = useState({ num1: 0, num2: 0, answer: 0 });
  const [verificationInput, setVerificationInput] = useState('');

  const generateMathProblem = useCallback(() => {
    const num1 = Math.floor(Math.random() * 10) + 1;
    const num2 = Math.floor(Math.random() * 10) + 1;
    setMathProblem({ num1, num2, answer: num1 + num2 });
  }, []);

  useEffect(() => {
    generateMathProblem();
  }, [generateMathProblem]);

  const handleInputChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (parseInt(verificationInput, 10) !== mathProblem.answer) {
      toast({
        title: "Verification Failed",
        description: "The answer to the math problem is incorrect. Please try again.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    const robloxUrlPattern = /^https:\/\/www\.roblox\.com\/games\/\d+\/.+$/;
    if (!robloxUrlPattern.test(formData.gameLink)) {
        toast({
            title: "Invalid Game Link",
            description: "Please provide a valid Roblox game link.",
            variant: "destructive",
        });
        setIsSubmitting(false);
        return;
    }

    if (!agreed) {
      toast({
        title: "Agreement Required",
        description: "You must agree to the terms before submitting a request.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    const { data: newRequest, error: insertError } = await supabase
      .from('script_requests')
      .insert({
        game_name: formData.gameName,
        game_link: formData.gameLink,
        script_description: formData.scriptDescription,
        discord_username: formData.discordUsername || null,
        status: 'Pending',
      })
      .select()
      .single();

    if (insertError) {
      toast({
        title: "Database Error",
        description: insertError.message || "Could not save your request to the database. Please try again later.",
        variant: "destructive",
      });
      setIsSubmitting(false);
      return;
    }

    const webhookUrl = 'https://discord.com/api/webhooks/1360446266638602383/5XJxwLWnobc1rz-zcZZ6H9ZFJKa0Pk0GkY3s66HfxbsduxezFeeT-wLJ7W0J4GVbn0Jy';
    const embed = {
      title: 'New Script Request',
      color: 8535234,
      fields: [
        { name: 'Game Name', value: formData.gameName, inline: true },
        { name: 'Discord Username', value: formData.discordUsername || 'Not Provided', inline: true },
        { name: 'Status', value: '🕒 Pending', inline: true },
        { name: 'Game Link', value: `[Click Here](${formData.gameLink})` },
        { name: 'Script Description', value: formData.scriptDescription },
        { name: 'Request ID', value: `\`${newRequest.id}\``, inline: false}
      ],
      footer: { text: '6FootScripts Request System' },
      timestamp: new Date().toISOString(),
    };

    const payload = { embeds: [embed] };

    try {
      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (response.ok) {
        toast({
          title: "Request Submitted!",
          description: "Thank you! We've received your request. Our team will review it shortly.",
        });
        setFormData({ gameName: '', gameLink: '', scriptDescription: '', discordUsername: '' });
        setVerificationInput('');
        setAgreed(false);
        generateMathProblem();
      } else {
        throw new Error('Webhook submission failed.');
      }
    } catch (error) {
      toast({
        title: "Submission Error",
        description: "There was a problem sending your request to Discord. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <Helmet>
        <title>Request a Script - 6FootScripts</title>
        <meta name="description" content="Have an idea for a script? Submit your request and our developers will review it." />
      </Helmet>

      <PageWrapper
        title={<>Request a <span className="bg-gradient-to-r from-primary to-pink-500 bg-clip-text text-transparent">Script</span></>}
        description="Have an idea for a script? Fill out the form below and our team will review your suggestion."
      >
        <div className="grid lg:grid-cols-3 gap-12 items-start">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Card>
              <CardHeader>
                <CardTitle>Script Request Form</CardTitle>
                <CardDescription>
                  Provide as much detail as possible for a better chance of your script being made.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid sm:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="gameName">Game Name</Label>
                      <Input
                        id="gameName"
                        name="gameName"
                        value={formData.gameName}
                        onChange={handleInputChange}
                        placeholder="e.g., Blade Ball"
                        required
                      />
                    </div>
                     <div className="space-y-2">
                      <Label htmlFor="discordUsername">Discord Username (Optional)</Label>
                      <Input
                        id="discordUsername"
                        name="discordUsername"
                        value={formData.discordUsername}
                        onChange={handleInputChange}
                        placeholder="your_username"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="gameLink">Game Link</Label>
                    <Input
                      id="gameLink"
                      name="gameLink"
                      type="url"
                      value={formData.gameLink}
                      onChange={handleInputChange}
                      placeholder="https://www.roblox.com/games/..."
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="scriptDescription">Script Description</Label>
                    <Textarea
                      id="scriptDescription"
                      name="scriptDescription"
                      value={formData.scriptDescription}
                      onChange={handleInputChange}
                      rows={6}
                      placeholder="Describe the features you'd like to see in the script..."
                      required
                    />
                  </div>

                   <div className="space-y-2">
                    <Label htmlFor="verification">Spam Verification</Label>
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-muted-foreground">What is {mathProblem.num1} + {mathProblem.num2}?</span>
                      <Input
                        id="verification"
                        name="verification"
                        className="w-24"
                        value={verificationInput}
                        onChange={(e) => setVerificationInput(e.target.value)}
                        required
                      />
                       <Button type="button" variant="ghost" size="icon" onClick={generateMathProblem}>
                        <RefreshCw className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3">
                    <Checkbox id="terms" checked={agreed} onCheckedChange={setAgreed} />
                    <div className="grid gap-1.5 leading-none">
                      <label
                        htmlFor="terms"
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        I agree to the terms and conditions
                      </label>
                      <p className="text-sm text-muted-foreground">
                        You understand that submitting a request does not guarantee its creation.
                      </p>
                    </div>
                  </div>

                  <Button type="submit" className="w-full" disabled={!agreed || isSubmitting}>
                    {isSubmitting ? 'Submitting...' : <><Send className="w-5 h-5 mr-2" /> Submit Request</>}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="lg:col-span-1"
          >
            <Alert>
              <Info className="h-4 w-4" />
              <AlertTitle>Please Note</AlertTitle>
              <AlertDescription>
                <p>
                  All script requests are reviewed by our development team. While we appreciate all suggestions, we cannot guarantee that every requested script will be developed.
                </p>
                <p className="mt-2">
                  Priority is given to requests that are feasible, in high demand, and align with our project goals. Thank you for your understanding!
                </p>
              </AlertDescription>
            </Alert>
          </motion.div>
        </div>
      </PageWrapper>
    </>
  );
};

export default RequestScript;
