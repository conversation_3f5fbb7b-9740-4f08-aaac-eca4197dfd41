
import React, { useState, useEffect } from 'react';
import { Helm<PERSON> } from 'react-helmet';
import { Link } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { supabase } from '@/lib/supabaseClient';
import { Loader2, Users, FileText, KeyRound, Code } from 'lucide-react';
import PageWrapper from '@/components/PageWrapper';
import { useAdminAuth } from '@/contexts/AdminAuthContext';
import { useToast } from '@/components/ui/use-toast';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

const AdminMainPanel = () => {
  const [stats, setStats] = useState({ requests: 0, keys: 0, visits: 0 });
  const [requestsChartData, setRequestsChartData] = useState([]);
  const [requestsStatusData, setRequestsStatusData] = useState([]);
  const [loading, setLoading] = useState(true);
  const { getAuthHeaders } = useAdminAuth();
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      const { data, error } = await supabase.functions.invoke('admin-api', {
        body: { action: 'get_stats', auth: getAuthHeaders() },
      });

      if (error) {
        toast({ variant: 'destructive', title: 'API Error', description: error.message });
        setLoading(false);
        return;
      }

      const { requestsCount, keysCount, visitsData, requestsData } = data;

      if (requestsData) {
        const monthlyData = requestsData.reduce((acc, req) => {
          const month = new Date(req.created_at).toLocaleString('default', { month: 'short', year: 'numeric' });
          acc[month] = (acc[month] || 0) + 1;
          return acc;
        }, {});
        const formattedChartData = Object.entries(monthlyData).map(([name, requests]) => ({ name, requests }));
        setRequestsChartData(formattedChartData);

        const statusData = requestsData.reduce((acc, req) => {
            acc[req.status] = (acc[req.status] || 0) + 1;
            return acc;
        }, {});
        const formattedStatusData = Object.entries(statusData).map(([name, value]) => ({ name, value }));
        setRequestsStatusData(formattedStatusData);
      }

      setStats({
        requests: requestsCount || 0,
        keys: keysCount || 0,
        visits: visitsData || 0,
      });

      setLoading(false);
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="w-16 h-16 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Admin Home - 6FootScripts</title>
      </Helmet>
      <PageWrapper title="Admin Home" description="Overview of your website's activity and management sections.">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.requests}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Admin Keys</CardTitle>
              <KeyRound className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.keys}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Unique Visitors</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.visits}</div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8">
            <h3 className="text-2xl font-bold tracking-tight mb-4">Management Sections</h3>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Link to="/admin/keys" className="hover:no-underline">
                    <Card className="hover:border-primary transition-all h-full flex flex-col">
                        <CardHeader>
                            <KeyRound className="h-8 w-8 text-primary mb-2" />
                            <CardTitle>Key Manager</CardTitle>
                        </CardHeader>
                        <CardContent className="flex-grow">
                            <p className="text-sm text-muted-foreground">Create and manage admin access keys.</p>
                        </CardContent>
                    </Card>
                </Link>
                <Link to="/admin/requests" className="hover:no-underline">
                    <Card className="hover:border-primary transition-all h-full flex flex-col">
                        <CardHeader>
                            <FileText className="h-8 w-8 text-primary mb-2" />
                            <CardTitle>Request Manager</CardTitle>
                        </CardHeader>
                        <CardContent className="flex-grow">
                            <p className="text-sm text-muted-foreground">View and handle user script requests.</p>
                        </CardContent>
                    </Card>
                </Link>
                <Link to="/admin/scripts" className="hover:no-underline">
                    <Card className="hover:border-primary transition-all h-full flex flex-col">
                        <CardHeader>
                            <Code className="h-8 w-8 text-primary mb-2" />
                            <CardTitle>Script Manager</CardTitle>
                        </CardHeader>
                        <CardContent className="flex-grow">
                            <p className="text-sm text-muted-foreground">Add, update, and manage all scripts.</p>
                        </CardContent>
                    </Card>
                </Link>
                <Link to="/admin/users" className="hover:no-underline">
                    <Card className="hover:border-primary transition-all h-full flex flex-col">
                        <CardHeader>
                            <Users className="h-8 w-8 text-primary mb-2" />
                            <CardTitle>User Manager</CardTitle>
                        </CardHeader>
                        <CardContent className="flex-grow">
                            <p className="text-sm text-muted-foreground">Manage whitelisted and blacklisted users.</p>
                        </CardContent>
                    </Card>
                </Link>
            </div>
        </div>

        <div className="mt-8 grid gap-8 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Script Requests by Month</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[350px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={requestsChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip contentStyle={{ backgroundColor: 'hsl(var(--background))', border: '1px solid hsl(var(--border))' }} />
                    <Legend />
                    <Bar dataKey="requests" fill="hsl(var(--primary))" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Script Requests by Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[350px]">
                <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                        <Pie data={requestsStatusData} dataKey="value" nameKey="name" cx="50%" cy="50%" outerRadius={120} fill="#8884d8" label>
                            {requestsStatusData.map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                        </Pie>
                        <Tooltip contentStyle={{ backgroundColor: 'hsl(var(--background))', border: '1px solid hsl(var(--border))' }} />
                        <Legend />
                    </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageWrapper>
    </>
  );
};

export default AdminMainPanel;
