import { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { supabase } from '@/lib/supabaseClient';
import { getHwid } from '@/lib/hwid';

export const useUserTracker = () => {
    const [isBlacklisted, setIsBlacklisted] = useState(false);
    const [loading, setLoading] = useState(true);
    const location = useLocation();

    useEffect(() => {
        const trackUser = async () => {
            setLoading(true);
            const hwid = getHwid();
            if (!hwid) {
                setLoading(false);
                return;
            }

            try {
                const { error: upsertError } = await supabase
                    .from('users')
                    .upsert({ hwid: hwid, status: 'neutral' }, { onConflict: 'hwid', ignoreDuplicates: true });

                if (upsertError) {
                    console.error("Error ensuring user exists:", upsertError);
                }

                const { data: user, error: userError } = await supabase
                    .from('users')
                    .select('status')
                    .eq('hwid', hwid)
                    .single();

                if (userError) {
                    console.error("User check error:", userError);
                } else if (user && user.status === 'blacklisted') {
                    setIsBlacklisted(true);
                    setLoading(false);
                    return;
                }

                if (!location.pathname.startsWith('/admin')) {
                    await supabase.from('page_visits').insert({ hwid, path: location.pathname });
                }
            } catch (error) {
                console.error("An error occurred during user tracking:", error);
            } finally {
                setLoading(false);
            }
        };

        trackUser();
    }, [location.pathname]);

    return { isBlacklisted, loading };
};